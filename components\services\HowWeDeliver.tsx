'use client';

import React from 'react';
import { steps } from './steps';


const HowWeDeliver = () => {
	return (
		<section className="w-full bg-white md:px-8 lg:px-20 py-8 md:py-12 lg:py-16">
			<div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-start gap-6 md:gap-8 text-black">
				<div className="flex-1 max-w-md px-0 md:px-4 lg:px-8 xl:px-12">
					<p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-2">Our Process</p>
					<h2 className="text-lg sm:text-4xl md:text-[24px] lg:text-[28px] font-normal text-black leading-[1.2]">How We<br><span className="whitespace-nowrap">Deliver Excellence.</span></h2>
				</div>
				<p className="flex-1 max-w-lg text-sm md:text-sm text-gray-600 leading-relaxed">
					Our streamlined approach ensures efficient, transparent, and successful <br />
					project execution. From discovery to deployment, our remote-first <br />
					teams deliver products that are intuitive, intelligent, and built to evolve.
				</p>
			</div>

			<div className="w-full bg-gray-50 md:px-8 lg:px-20 py-8 md:py-12 lg:py-16 mt-8 md:mt-12 lg:mt-16">
				<div className="relative max-w-6xl mx-auto ml-0 mr-0 pl-0 md:ml-0 md:mr-0 md:pl-4 lg:ml-[120px] lg:mr-[120px] lg:pl-4">
					{/* Connecting Line: only between the first and last step circles */}
					<div className="hidden md:block absolute top-8 left-[12.5%] right-[12.5%] h-0.5 bg-blue-200 z-0" />
					<div className="flex flex-col md:flex-row items-center justify-center gap-8 md:gap-5">
						{steps.map((step, index) => (
							<div
								key={index}
								className="flex flex-col items-center text-center max-w-xs w-full mx-auto z-10 md:w-1/4 relative mb-8 md:mb-0"
							>
								{/* Circles: bring to front */}
								<div className="w-12 h-12 flex items-center justify-center rounded-full bg-[#2B7FFF] text-white font-semibold mb-4 mt-1 z-10">
									{step.number}
								</div>
								<h3 className="font-semibold text-gray-900 mb-2">{step.title}</h3>
								<p className="text-gray-600 text-[12px]">{step.description}</p>
							</div>
						))}
					</div>
				</div>
			</div>
		</section>
	);
};

export default HowWeDeliver;
