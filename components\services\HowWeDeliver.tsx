'use client';

import React from 'react';
import { steps } from './steps';


const HowWeDeliver = () => {
	return (
		<section className="w-full bg-gray-50 py-16 md:py-20 lg:py-24">
			<div className="max-w-6xl mx-auto px-4 md:px-8">
				{/* Header Section - Centered */}
				<div className="text-center mb-16">
					<p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-3">
						OUR PROCESS
					</p>
					<h2 className="text-[32px] font-semibold text-gray-900 mb-4">
						How We Deliver
					</h2>
					<p className="text-gray-600 text-base md:text-lg max-w-4xl mx-auto leading-relaxed whitespace-nowrap">
						Our streamlined approach ensures efficient, transparent, and successful project execution.
					</p>
				</div>

				{/* Process Steps */}
				<div className="relative">
					{/* Connecting Line */}
					<div className="hidden md:block absolute top-6 left-0 right-0 h-0.5 bg-gray-300 z-0"
						 style={{left: '12.5%', right: '12.5%'}} />

					<div className="grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-4">
						{steps.map((step, index) => (
							<div
								key={index}
								className="flex flex-col items-center text-center relative z-10"
							>
								{/* Step Circle */}
								<div className="w-12 h-12 flex items-center justify-center rounded-full bg-[#2B7FFF] text-white font-bold text-sm mb-6 relative z-10">
									{step.number}
								</div>

								{/* Step Content */}
								<h3 className="font-bold text-gray-900 text-[18px] mb-3">
									{step.title}
								</h3>
								<p className="text-gray-600 text-sm leading-relaxed">
									{step.description}
								</p>
							</div>
						))}
					</div>
				</div>
			</div>
		</section>
	);
};

export default HowWeDeliver;
